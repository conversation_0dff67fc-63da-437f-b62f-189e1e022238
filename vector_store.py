from langchain_postgres.vectorstores import PGVector
import os
from dotenv import load_dotenv
load_dotenv()
from sqlalchemy import create_engine
from langchain_google_genai import GoogleGenerativeAIEmbeddings
import logging
from langchain.tools.retriever import create_retriever_tool
from google.genai.types import Tool, FunctionDeclaration

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)  # Set to DEBUG for more logs if needed

# Load Google Application Credentials
credential_path = "gen-lang-client-0077680037-9adbc1746e59.json"
if not os.path.exists(credential_path):
    raise FileNotFoundError(f"Credential file '{credential_path}' not found.")
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credential_path

# Initialize Embedding Model
def initialize_embeddings():
    try:
        embeddings = GoogleGenerativeAIEmbeddings(
            model="models/text-embedding-004",
        )
        return embeddings
    except Exception as e:
        logger.error(f"Failed to initialize Google Generative AI embeddings: {str(e)}")
        raise RuntimeError(f"Failed to initialize embeddings: {str(e)}")

# PostgreSQL DB Connection
def get_engine():
    username = os.getenv('db_user')
    password = os.getenv('db_password')
    hostname = os.getenv('db_host')
    port = os.getenv('db_port')
    database_name = os.getenv('db_name')

    return create_engine(f'postgresql+psycopg://{username}:{password}@{hostname}:{port}/{database_name}')

embedding = initialize_embeddings()

collection_name = "ct2"

connection = get_engine()

vector_store = PGVector(
    embeddings=embedding,
    collection_name=collection_name,
    connection=connection,
    use_jsonb=True,
)



def vector_search(query: str):
    try:
        results = vector_store.similarity_search(k=10, query=query)
        return results
    except Exception as e:
        logger.error(f"Failed to perform vector search: {str(e)}")
        raise RuntimeError(f"Failed to perform vector search: {str(e)}")

# Create the tool for Google Gemini Live API
Vector_tool = Tool(
    function_declarations=[
        FunctionDeclaration(
            name="document_search",
            description="Search documents and knowledge base",
            parameters={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The query to search for",
                    },
                },
                "required": ["query"],
            },
        ),
    ],
)

# Async function to handle document search for the Live API
async def handle_document_search(query: str):
    """
    Handle document search function call from Gemini Live API

    Args:
        query: The search query string

    Returns:
        Formatted search results for the model
    """
    try:
        # Perform the vector search
        results = vector_search(query)

        # Format results for the model
        if results:
            formatted_results = []
            for i, result in enumerate(results, 1):
                content = result.page_content if hasattr(result, 'page_content') else str(result)
                metadata = result.metadata if hasattr(result, 'metadata') else {}

                formatted_results.append(f"Result {i}:\nContent: {content}\nMetadata: {metadata}\n")

            return {
                "function_response": {
                    "name": "document_search",
                    "response": {
                        "results": "\n".join(formatted_results),
                        "total_results": len(results)
                    }
                }
            }
        else:
            return {
                "function_response": {
                    "name": "document_search",
                    "response": {
                        "results": "No documents found matching your query.",
                        "total_results": 0
                    }
                }
            }

    except Exception as e:
        logger.error(f"Error in handle_document_search: {str(e)}")
        return {
            "function_response": {
                "name": "document_search",
                "response": {
                    "error": f"Search failed: {str(e)}",
                    "total_results": 0
                }
            }
        }



